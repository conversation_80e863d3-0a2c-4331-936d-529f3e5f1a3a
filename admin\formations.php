<?php
require_once 'includes/auth.php';
require_once '../includes/config.php';

// Vérifier l'authentification
requireLogin();

$page_title = "Gestion des formations";
$current_user = getCurrentUser();

// Gestion des actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

try {
    $pdo = getDBConnection();
    
    // Traitement des actions POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['bulk_action']) && isset($_POST['selected_items'])) {
            // Actions en lot
            $bulk_action = $_POST['bulk_action'];
            $selected_items = $_POST['selected_items'];
            
            foreach ($selected_items as $item_id) {
                switch ($bulk_action) {
                    case 'activate':
                        $stmt = $pdo->prepare("UPDATE formations SET statut = 'active' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'deactivate':
                        $stmt = $pdo->prepare("UPDATE formations SET statut = 'inactive' WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                    case 'delete':
                        $stmt = $pdo->prepare("DELETE FROM formations WHERE id = ?");
                        $stmt->execute([$item_id]);
                        break;
                }
            }
            $message = "Actions appliquées avec succès";
        }
        
        // Sauvegarde de formation
        if (isset($_POST['save_formation'])) {
            $nom = trim($_POST['nom'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $duree = trim($_POST['duree'] ?? '');
            $niveau = $_POST['niveau'] ?? '';
            $secteur = $_POST['secteur'] ?? '';
            $prix = $_POST['prix'] ?? null;
            $places_disponibles = $_POST['places_disponibles'] ?? 0;
            $date_debut = $_POST['date_debut'] ?? null;
            $date_fin = $_POST['date_fin'] ?? null;
            $lieu = trim($_POST['lieu'] ?? '');
            $statut = $_POST['statut'] ?? 'active';
            
            if (empty($nom) || empty($description) || empty($niveau) || empty($secteur)) {
                $error = "Les champs nom, description, niveau et secteur sont obligatoires";
            } else {
                try {
                    if ($action === 'add') {
                        $stmt = $pdo->prepare("
                            INSERT INTO formations (nom, description, duree, niveau, secteur, prix, places_disponibles, date_debut, date_fin, lieu, statut, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$nom, $description, $duree, $niveau, $secteur, $prix, $places_disponibles, $date_debut, $date_fin, $lieu, $statut, $current_user['id']]);
                        $message = "Formation créée avec succès";
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE formations 
                            SET nom = ?, description = ?, duree = ?, niveau = ?, secteur = ?, prix = ?, places_disponibles = ?, date_debut = ?, date_fin = ?, lieu = ?, statut = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$nom, $description, $duree, $niveau, $secteur, $prix, $places_disponibles, $date_debut, $date_fin, $lieu, $statut, $id]);
                        $message = "Formation modifiée avec succès";
                    }
                    
                    header("Location: formations.php?message=" . urlencode($message));
                    exit;
                    
                } catch (PDOException $e) {
                    $error = "Erreur lors de la sauvegarde : " . $e->getMessage();
                }
            }
        }
    }
    
    // Traitement des actions GET
    if ($action === 'delete' && $id) {
        $stmt = $pdo->prepare("DELETE FROM formations WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Formation supprimée avec succès";
        $action = 'list';
    } elseif ($action === 'toggle_status' && $id) {
        $stmt = $pdo->prepare("UPDATE formations SET statut = CASE WHEN statut = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?");
        $stmt->execute([$id]);
        $message = "Statut modifié avec succès";
        $action = 'list';
    }
    
    // Récupération des formations pour la liste
    if ($action === 'list') {
        $filter_status = $_GET['status'] ?? '';
        $filter_secteur = $_GET['secteur'] ?? '';
        $filter_niveau = $_GET['niveau'] ?? '';
        $search = $_GET['search'] ?? '';
        
        $where_conditions = [];
        $params = [];
        
        if ($filter_status) {
            $where_conditions[] = "statut = ?";
            $params[] = $filter_status;
        }
        
        if ($filter_secteur) {
            $where_conditions[] = "secteur = ?";
            $params[] = $filter_secteur;
        }
        
        if ($filter_niveau) {
            $where_conditions[] = "niveau = ?";
            $params[] = $filter_niveau;
        }
        
        if ($search) {
            $where_conditions[] = "(nom LIKE ? OR description LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $stmt = $pdo->prepare("
            SELECT f.*, u.username as created_by_name 
            FROM formations f 
            LEFT JOIN admin_users u ON f.created_by = u.id 
            $where_clause 
            ORDER BY f.created_at DESC
        ");
        $stmt->execute($params);
        $formations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = "Erreur de base de données : " . $e->getMessage();
}

include 'includes/header.php';
?>

<?php if ($message): ?>
    <div class="alert alert-success" data-dismiss="auto">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-triangle"></i>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <div class="page-header">
        <div class="page-title">
            <h1>Gestion des formations</h1>
            <p>Gérez le catalogue de formations de votre établissement</p>
        </div>
        <div class="page-actions">
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Ajouter une formation
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="filters-bar">
        <form method="GET" class="filters-form">
            <div class="filter-group">
                <input type="text" name="search" placeholder="Rechercher..." 
                       value="<?php echo htmlspecialchars($search ?? ''); ?>">
            </div>
            
            <div class="filter-group">
                <select name="status">
                    <option value="">Tous les statuts</option>
                    <option value="active" <?php echo ($filter_status === 'active') ? 'selected' : ''; ?>>Active</option>
                    <option value="inactive" <?php echo ($filter_status === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                    <option value="complete" <?php echo ($filter_status === 'complete') ? 'selected' : ''; ?>>Terminée</option>
                </select>
            </div>
            
            <div class="filter-group">
                <select name="secteur">
                    <option value="">Tous les secteurs</option>
                    <option value="mecanique" <?php echo ($filter_secteur === 'mecanique') ? 'selected' : ''; ?>>Mécanique</option>
                    <option value="electricite" <?php echo ($filter_secteur === 'electricite') ? 'selected' : ''; ?>>Électricité</option>
                    <option value="automobile" <?php echo ($filter_secteur === 'automobile') ? 'selected' : ''; ?>>Automobile</option>
                    <option value="navale" <?php echo ($filter_secteur === 'navale') ? 'selected' : ''; ?>>Navale</option>
                    <option value="thermique" <?php echo ($filter_secteur === 'thermique') ? 'selected' : ''; ?>>Génie Thermique</option>
                    <option value="general" <?php echo ($filter_secteur === 'general') ? 'selected' : ''; ?>>Général</option>
                </select>
            </div>
            
            <div class="filter-group">
                <select name="niveau">
                    <option value="">Tous les niveaux</option>
                    <option value="CAP" <?php echo ($filter_niveau === 'CAP') ? 'selected' : ''; ?>>CAP</option>
                    <option value="BTS" <?php echo ($filter_niveau === 'BTS') ? 'selected' : ''; ?>>BTS</option>
                    <option value="Licence Pro" <?php echo ($filter_niveau === 'Licence Pro') ? 'selected' : ''; ?>>Licence Pro</option>
                    <option value="Formation Continue" <?php echo ($filter_niveau === 'Formation Continue') ? 'selected' : ''; ?>>Formation Continue</option>
                    <option value="Formation Courte" <?php echo ($filter_niveau === 'Formation Courte') ? 'selected' : ''; ?>>Formation Courte</option>
                    <option value="Apprentissage" <?php echo ($filter_niveau === 'Apprentissage') ? 'selected' : ''; ?>>Apprentissage</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-search"></i>
                Filtrer
            </button>
        </form>
    </div>
    
    <!-- Actions en lot -->
    <form method="POST" id="bulk-form">
        <div class="bulk-actions" style="display: none;">
            <select name="bulk_action">
                <option value="">Actions en lot</option>
                <option value="activate">Activer</option>
                <option value="deactivate">Désactiver</option>
                <option value="delete">Supprimer</option>
            </select>
            <button type="submit" class="btn btn-secondary">Appliquer</button>
        </div>
        
        <!-- Tableau des formations -->
        <div class="data-table">
            <table>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all"></th>
                        <th>Formation</th>
                        <th>Niveau</th>
                        <th>Secteur</th>
                        <th>Durée</th>
                        <th>Places</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($formations)): ?>
                        <tr>
                            <td colspan="8" class="no-data">
                                <i class="fas fa-graduation-cap"></i>
                                <p>Aucune formation trouvée</p>
                                <a href="?action=add" class="btn btn-primary">Créer la première formation</a>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($formations as $formation): ?>
                            <tr class="data-row" data-status="<?php echo $formation['statut']; ?>">
                                <td>
                                    <input type="checkbox" name="selected_items[]" value="<?php echo $formation['id']; ?>" class="item-checkbox">
                                </td>
                                <td>
                                    <div class="item-title">
                                        <strong><?php echo htmlspecialchars($formation['nom']); ?></strong>
                                        <div class="item-meta">
                                            <?php if ($formation['prix']): ?>
                                                <?php echo number_format($formation['prix'], 2); ?>€ - 
                                            <?php endif; ?>
                                            <?php echo htmlspecialchars($formation['lieu'] ?? 'Lieu non défini'); ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="level-badge <?php echo strtolower(str_replace(' ', '-', $formation['niveau'])); ?>">
                                        <?php echo $formation['niveau']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="category-badge <?php echo $formation['secteur']; ?>">
                                        <?php echo ucfirst($formation['secteur']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($formation['duree'] ?? 'Non définie'); ?></td>
                                <td>
                                    <?php if ($formation['places_disponibles']): ?>
                                        <span class="places-count"><?php echo $formation['places_disponibles']; ?> places</span>
                                    <?php else: ?>
                                        <span class="text-muted">Illimité</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $formation['statut']; ?>">
                                        <?php echo ucfirst($formation['statut']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=edit&id=<?php echo $formation['id']; ?>" class="btn-icon" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="?action=toggle_status&id=<?php echo $formation['id']; ?>" class="btn-icon" title="Changer le statut">
                                            <i class="fas fa-toggle-<?php echo $formation['statut'] === 'active' ? 'on' : 'off'; ?>"></i>
                                        </a>
                                        <a href="?action=delete&id=<?php echo $formation['id']; ?>" class="btn-icon btn-danger" 
                                           data-action="delete" data-item="<?php echo htmlspecialchars($formation['nom']); ?>" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </form>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <?php
    $formation = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM formations WHERE id = ?");
        $stmt->execute([$id]);
        $formation = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$formation) {
            $error = "Formation non trouvée";
            $action = 'list';
        }
    }
    ?>

    <div class="page-header">
        <div class="page-title">
            <h1><?php echo $action === 'add' ? 'Ajouter' : 'Modifier'; ?> une formation</h1>
            <p>Créez ou modifiez une formation de votre catalogue</p>
        </div>
        <div class="page-actions">
            <a href="formations.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Retour à la liste
            </a>
        </div>
    </div>

    <form method="POST" class="form-container" data-autosave="formation_<?php echo $id ?? 'new'; ?>">
        <div class="form-grid">
            <div class="form-main">
                <div class="form-section">
                    <h3>Informations générales</h3>

                    <div class="form-group">
                        <label for="nom">Nom de la formation *</label>
                        <input type="text" id="nom" name="nom" required
                               value="<?php echo htmlspecialchars($formation['nom'] ?? ''); ?>"
                               placeholder="Ex: Technicien en Maintenance Industrielle">
                    </div>

                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" rows="6" required class="rich-editor"
                                  placeholder="Description détaillée de la formation, objectifs, compétences acquises..."><?php echo htmlspecialchars($formation['description'] ?? ''); ?></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="duree">Durée</label>
                            <input type="text" id="duree" name="duree"
                                   value="<?php echo htmlspecialchars($formation['duree'] ?? ''); ?>"
                                   placeholder="Ex: 2 ans, 6 mois, 400h">
                        </div>

                        <div class="form-group">
                            <label for="lieu">Lieu</label>
                            <input type="text" id="lieu" name="lieu"
                                   value="<?php echo htmlspecialchars($formation['lieu'] ?? ''); ?>"
                                   placeholder="Ex: Campus principal, À distance">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="prix">Prix (€)</label>
                            <input type="number" id="prix" name="prix" step="0.01" min="0"
                                   value="<?php echo $formation['prix'] ?? ''; ?>"
                                   placeholder="0.00">
                        </div>

                        <div class="form-group">
                            <label for="places_disponibles">Places disponibles</label>
                            <input type="number" id="places_disponibles" name="places_disponibles" min="0"
                                   value="<?php echo $formation['places_disponibles'] ?? ''; ?>"
                                   placeholder="0 = illimité">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="date_debut">Date de début</label>
                            <input type="date" id="date_debut" name="date_debut"
                                   value="<?php echo $formation['date_debut'] ?? ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="date_fin">Date de fin</label>
                            <input type="date" id="date_fin" name="date_fin"
                                   value="<?php echo $formation['date_fin'] ?? ''; ?>">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-sidebar">
                <div class="form-section">
                    <h3>Classification</h3>

                    <div class="form-group">
                        <label for="niveau">Niveau *</label>
                        <select id="niveau" name="niveau" required>
                            <option value="">Sélectionner un niveau</option>
                            <option value="CAP" <?php echo ($formation['niveau'] ?? '') === 'CAP' ? 'selected' : ''; ?>>CAP</option>
                            <option value="BTS" <?php echo ($formation['niveau'] ?? '') === 'BTS' ? 'selected' : ''; ?>>BTS</option>
                            <option value="Licence Pro" <?php echo ($formation['niveau'] ?? '') === 'Licence Pro' ? 'selected' : ''; ?>>Licence Pro</option>
                            <option value="Formation Continue" <?php echo ($formation['niveau'] ?? '') === 'Formation Continue' ? 'selected' : ''; ?>>Formation Continue</option>
                            <option value="Formation Courte" <?php echo ($formation['niveau'] ?? '') === 'Formation Courte' ? 'selected' : ''; ?>>Formation Courte</option>
                            <option value="Apprentissage" <?php echo ($formation['niveau'] ?? '') === 'Apprentissage' ? 'selected' : ''; ?>>Apprentissage</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="secteur">Secteur *</label>
                        <select id="secteur" name="secteur" required>
                            <option value="">Sélectionner un secteur</option>
                            <option value="mecanique" <?php echo ($formation['secteur'] ?? '') === 'mecanique' ? 'selected' : ''; ?>>Mécanique</option>
                            <option value="electricite" <?php echo ($formation['secteur'] ?? '') === 'electricite' ? 'selected' : ''; ?>>Électricité</option>
                            <option value="automobile" <?php echo ($formation['secteur'] ?? '') === 'automobile' ? 'selected' : ''; ?>>Automobile</option>
                            <option value="navale" <?php echo ($formation['secteur'] ?? '') === 'navale' ? 'selected' : ''; ?>>Navale</option>
                            <option value="thermique" <?php echo ($formation['secteur'] ?? '') === 'thermique' ? 'selected' : ''; ?>>Génie Thermique</option>
                            <option value="general" <?php echo ($formation['secteur'] ?? '') === 'general' ? 'selected' : ''; ?>>Général</option>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <h3>Statut</h3>

                    <div class="form-group">
                        <label for="statut">Statut de la formation</label>
                        <select id="statut" name="statut">
                            <option value="active" <?php echo ($formation['statut'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($formation['statut'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="complete" <?php echo ($formation['statut'] ?? '') === 'complete' ? 'selected' : ''; ?>>Terminée</option>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" name="save_formation" class="btn btn-primary btn-full">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer' : 'Mettre à jour'; ?>
                    </button>

                    <?php if ($action === 'edit'): ?>
                        <a href="formations.php?action=delete&id=<?php echo $id; ?>"
                           class="btn btn-danger btn-full"
                           data-action="delete" data-item="<?php echo htmlspecialchars($formation['nom']); ?>">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </form>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
